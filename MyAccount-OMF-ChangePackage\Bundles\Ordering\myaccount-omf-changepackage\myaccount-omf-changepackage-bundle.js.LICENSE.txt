/*! myaccount-omf-changepackage (bundle) 1.0.0 | bwtk 6.1.0 | 2025-08-25T14:58:40.192Z */

/*!***********************************!*\
  !*** ../src/index.ts + 6 modules ***!
  \***********************************/

/*!******************************************!*\
  !*** ./js-search/dist/commonjs/index.js ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./js-search/dist/commonjs/Search.js ***!
  \*******************************************/

/*!*********************************************!*\
  !*** ./omf-changepackage-tv/dist/widget.js ***!
  \*********************************************/

/*!*************************************************!*\
  !*** ./js-search/dist/commonjs/StopWordsMap.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./omf-changepackage-review/dist/widget.js ***!
  \*************************************************/

/*!***************************************************!*\
  !*** ./omf-changepackage-internet/dist/widget.js ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./js-search/dist/commonjs/Sanitizer/index.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./js-search/dist/commonjs/Tokenizer/index.js ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./js-search/dist/commonjs/TokenHighlighter.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./omf-changepackage-navigation/dist/widget.js ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./js-search/dist/commonjs/SearchIndex/index.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./omf-changepackage-appointment/dist/widget.js ***!
  \******************************************************/

/*!********************************************************!*\
  !*** ./js-search/dist/commonjs/IndexStrategy/index.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./js-search/dist/commonjs/getNestedFieldValue.js ***!
  \********************************************************/

/*!**************************************************************!*\
  !*** ./js-search/dist/commonjs/Tokenizer/SimpleTokenizer.js ***!
  \**************************************************************/

/*!****************************************************************!*\
  !*** ./js-search/dist/commonjs/Tokenizer/StemmingTokenizer.js ***!
  \****************************************************************/

/*!*****************************************************************!*\
  !*** ./js-search/dist/commonjs/Sanitizer/LowerCaseSanitizer.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./js-search/dist/commonjs/SearchIndex/TfIdfSearchIndex.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./js-search/dist/commonjs/Tokenizer/StopWordsTokenizer.js ***!
  \*****************************************************************/

/*!*********************************************************************!*\
  !*** ./js-search/dist/commonjs/Sanitizer/CaseSensitiveSanitizer.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./js-search/dist/commonjs/SearchIndex/UnorderedSearchIndex.js ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./js-search/dist/commonjs/IndexStrategy/PrefixIndexStrategy.js ***!
  \**********************************************************************/

/*!*************************************************************************!*\
  !*** ./js-search/dist/commonjs/IndexStrategy/ExactWordIndexStrategy.js ***!
  \*************************************************************************/

/*!***************************************************************************!*\
  !*** ./omf-changepackage-components/dist/omf-changepackage-components.js ***!
  \***************************************************************************/

/*!*****************************************************************************!*\
  !*** ./js-search/dist/commonjs/IndexStrategy/AllSubstringsIndexStrategy.js ***!
  \*****************************************************************************/

/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/

/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/

/*!*****************************************************************************************************!*\
  !*** external {"root":"ReactDOM","commonjs2":"react-dom","commonjs":"react-dom","amd":"react-dom"} ***!
  \*****************************************************************************************************/

/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/

/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/

/*!**************************************************************************************************************!*\
  !*** external {"root":"Rx","commonjs":"rxjs/operators","commonjs2":"rxjs/operators","amd":"rxjs/operators"} ***!
  \**************************************************************************************************************/

/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/

/*!********************************************************************************************************************************!*\
  !*** external {"root":"ReactRouterDOM","commonjs2":"react-router-dom","commonjs":"react-router-dom","amd":"react-router-dom"} ***!
  \********************************************************************************************************************************/

/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
